<template>
  <el-dialog
    v-model="visible"
    :title="$t('device.arrange.title')"
    width="90%"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    destroy-on-close
    append-to-body
    class="el-dialog-flex el-dialog--beautify"
    @closed="onClosed"
  >
    <div class="flex flex-col h-full">
      <!-- 工具栏 -->
      <div class="flex items-center justify-between mb-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div class="flex items-center space-x-4">
          <el-button
            type="primary"
            :icon="Plus"
            @click="showAddDeviceDialog = true"
          >
            {{ $t('device.arrange.addDevice') }}
          </el-button>
          <el-button
            :icon="Setting"
            @click="showGlobalConfigDialog = true"
          >
            {{ $t('device.arrange.globalConfig') }}
          </el-button>
        </div>
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-600 dark:text-gray-400">
            {{ $t('device.arrange.screenSize') }}: {{ screenInfo.width }} × {{ screenInfo.height }}
          </span>
        </div>
      </div>

      <!-- 编排区域 -->
      <div class="flex-1 relative">
        <div
          ref="arrangeContainer"
          class="w-full h-full bg-gray-100 dark:bg-gray-900 rounded-lg overflow-hidden"
          :style="containerStyle"
        >
          <GridLayout
            v-if="gridLayoutReady"
            v-model:layout="layout"
            :col-num="gridCols"
            :row-height="gridRowHeight"
            :is-draggable="true"
            :is-resizable="true"
            :vertical-compact="true"
            :use-css-transforms="true"
            :margin="[10, 10]"
            class="arrange-grid"
            @layout-updated="onLayoutUpdated"
          >
            <template #item="{ item }">
              <div class="device-item">
                <div class="device-header">
                  <span class="device-name">{{ getDeviceName(item.i) }}</span>
                  <el-button
                    type="danger"
                    :icon="Close"
                    size="small"
                    circle
                    @click="removeDevice(item.i)"
                  />
                </div>
                <div class="device-preview">
                  <div class="device-screen">
                    <span class="device-id">{{ item.i }}</span>
                    <div class="device-info">
                      <div>{{ Math.round(item.realWidth) }} × {{ Math.round(item.realHeight) }}</div>
                      <div>{{ Math.round(item.realX) }}, {{ Math.round(item.realY) }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </GridLayout>
        </div>
      </div>

      <!-- 底部操作栏 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-2">
          <el-button @click="resetLayout">
            {{ $t('device.arrange.reset') }}
          </el-button>
          <el-button @click="previewLayout">
            {{ $t('device.arrange.preview') }}
          </el-button>
        </div>
        <div class="flex items-center space-x-2">
          <el-button @click="close">
            {{ $t('common.cancel') }}
          </el-button>
          <el-button type="primary" @click="saveLayout">
            {{ $t('common.save') }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 添加设备对话框 -->
    <el-dialog
      v-model="showAddDeviceDialog"
      :title="$t('device.arrange.addDevice')"
      width="500px"
      append-to-body
    >
      <div class="space-y-4">
        <div v-for="device in availableDevices" :key="device.id" class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="flex items-center space-x-3">
            <div class="w-3 h-3 rounded-full" :class="device.wifi ? 'bg-blue-500' : 'bg-green-500'"></div>
            <div>
              <div class="font-medium">
                {{ device.remark || device.name }}
              </div>
              <div class="text-sm text-gray-500">
                {{ device.id }}
              </div>
            </div>
          </div>
          <el-button
            type="primary"
            size="small"
            :disabled="isDeviceInLayout(device.id)"
            @click="addDeviceToLayout(device)"
          >
            {{ isDeviceInLayout(device.id) ? $t('device.arrange.added') : $t('device.arrange.add') }}
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 全局配置对话框 -->
    <el-dialog
      v-model="showGlobalConfigDialog"
      :title="$t('device.arrange.globalConfig')"
      width="400px"
      append-to-body
    >
      <el-form :model="globalConfig" label-width="120px">
        <el-form-item :label="$t('device.arrange.defaultWidth')">
          <el-input-number
            v-model="globalConfig['--window-width']"
            :min="100"
            :max="screenInfo.width"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item :label="$t('device.arrange.defaultHeight')">
          <el-input-number
            v-model="globalConfig['--window-height']"
            :min="100"
            :max="screenInfo.height"
            controls-position="right"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showGlobalConfigDialog = false">
          {{ $t('common.cancel') }}
        </el-button>
        <el-button type="primary" @click="saveGlobalConfig">
          {{ $t('common.save') }}
        </el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { GridLayout } from 'grid-layout-plus'
import { Close, Plus, Setting } from '@element-plus/icons-vue'

const visible = ref(false)
const showAddDeviceDialog = ref(false)
const showGlobalConfigDialog = ref(false)
const arrangeContainer = ref(null)
const gridLayoutReady = ref(false)

// 屏幕信息
const screenInfo = ref({
  width: 1920,
  height: 1080,
})

// 网格配置
const gridCols = ref(24)
const gridRowHeight = ref(30)
const containerStyle = ref({})

// 布局数据
const layout = ref([])
const availableDevices = ref([])
const globalConfig = ref({
  '--window-width': 300,
  '--window-height': 600,
})

// 获取屏幕信息
async function getScreenInfo() {
  try {
    const display = await window.electron.screen.getPrimaryDisplay()
    screenInfo.value = {
      width: display.workAreaSize.width,
      height: display.workAreaSize.height,
    }
  }
  catch (error) {
    console.warn('Failed to get screen info:', error)
    // 使用默认值
    screenInfo.value = {
      width: 1920,
      height: 1080,
    }
  }
}

// 计算容器样式
function calculateContainerStyle() {
  const { width, height } = screenInfo.value
  const maxWidth = 800
  const aspectRatio = height / width
  const containerHeight = Math.min(maxWidth * aspectRatio, 600)

  containerStyle.value = {
    width: `${maxWidth}px`,
    height: `${containerHeight}px`,
    margin: '0 auto',
  }

  gridRowHeight.value = Math.floor(containerHeight / 20)
}

// 获取设备列表
async function loadDevices() {
  try {
    const devices = window.appStore.get('device') || {}
    availableDevices.value = Object.values(devices).filter(device => device.id)
  }
  catch (error) {
    console.error('Failed to load devices:', error)
  }
}

// 加载现有布局
function loadExistingLayout() {
  const scrcpyConfig = window.appStore.get('scrcpy') || {}
  const globalConf = scrcpyConfig.global || {}

  globalConfig.value = {
    '--window-width': Number.parseInt(globalConf['--window-width']) || 300,
    '--window-height': Number.parseInt(globalConf['--window-height']) || 600,
  }

  // 加载设备级配置到布局
  const deviceConfigs = Object.keys(scrcpyConfig).filter(key => key !== 'global' && key !== 'deviceScope')
  layout.value = deviceConfigs.map((deviceId) => {
    const config = scrcpyConfig[deviceId] || {}
    const width = Number.parseInt(config['--window-width']) || globalConfig.value['--window-width']
    const height = Number.parseInt(config['--window-height']) || globalConfig.value['--window-height']
    const x = Number.parseInt(config['--window-x']) || 0
    const y = Number.parseInt(config['--window-y']) || 0

    return {
      i: deviceId,
      x: Math.floor(x / (screenInfo.value.width / gridCols.value)),
      y: Math.floor(y / gridRowHeight.value),
      w: Math.max(2, Math.floor(width / (screenInfo.value.width / gridCols.value))),
      h: Math.max(2, Math.floor(height / gridRowHeight.value)),
      realX: x,
      realY: y,
      realWidth: width,
      realHeight: height,
    }
  })
}

function getDeviceName(deviceId) {
  const device = availableDevices.value.find(d => d.id === deviceId)
  return device ? (device.remark || device.name) : deviceId
}

function isDeviceInLayout(deviceId) {
  return layout.value.some(item => item.i === deviceId)
}

function addDeviceToLayout(device) {
  if (isDeviceInLayout(device.id))
    return

  const newItem = {
    i: device.id,
    x: 0,
    y: 0,
    w: 4,
    h: 6,
    realX: 0,
    realY: 0,
    realWidth: globalConfig.value['--window-width'],
    realHeight: globalConfig.value['--window-height'],
  }

  layout.value.push(newItem)
  showAddDeviceDialog.value = false
}

function removeDevice(deviceId) {
  const index = layout.value.findIndex(item => item.i === deviceId)
  if (index > -1) {
    layout.value.splice(index, 1)
  }
}

function onLayoutUpdated(newLayout) {
  // 更新真实坐标和尺寸
  newLayout.forEach((item) => {
    const scaleX = screenInfo.value.width / gridCols.value
    const scaleY = gridRowHeight.value

    item.realX = Math.round(item.x * scaleX)
    item.realY = Math.round(item.y * scaleY)
    item.realWidth = Math.round(item.w * scaleX)
    item.realHeight = Math.round(item.h * scaleY)
  })
}

function resetLayout() {
  layout.value = []
}

function previewLayout() {
  ElMessage.info($t('device.arrange.previewTip'))
}

function saveGlobalConfig() {
  window.appStore.set('scrcpy.global.--window-width', globalConfig.value['--window-width'].toString())
  window.appStore.set('scrcpy.global.--window-height', globalConfig.value['--window-height'].toString())
  showGlobalConfigDialog.value = false
  ElMessage.success($t('common.saveSuccess'))
}

function saveLayout() {
  try {
    // 保存全局配置
    window.appStore.set('scrcpy.global.--window-x', '0')
    window.appStore.set('scrcpy.global.--window-y', '0')

    // 保存设备级配置
    layout.value.forEach((item) => {
      window.appStore.set(`scrcpy.${item.i}.--window-width`, item.realWidth.toString())
      window.appStore.set(`scrcpy.${item.i}.--window-height`, item.realHeight.toString())
      window.appStore.set(`scrcpy.${item.i}.--window-x`, item.realX.toString())
      window.appStore.set(`scrcpy.${item.i}.--window-y`, item.realY.toString())
    })

    ElMessage.success($t('common.saveSuccess'))
    close()
  }
  catch (error) {
    console.error('Failed to save layout:', error)
    ElMessage.error($t('common.saveError'))
  }
}

function close() {
  visible.value = false
}

function onClosed() {
  layout.value = []
  showAddDeviceDialog.value = false
  showGlobalConfigDialog.value = false
  gridLayoutReady.value = false
}

async function open() {
  visible.value = true
  await getScreenInfo()
  calculateContainerStyle()
  await loadDevices()
  loadExistingLayout()

  // 延迟初始化 GridLayout 以确保容器已渲染
  nextTick(() => {
    gridLayoutReady.value = true
  })
}

defineExpose({
  open,
  close,
})
</script>

<style lang="postcss" scoped>
.arrange-grid {
  @apply w-full h-full;
}

:deep(.vgl-layout) {
  @apply bg-transparent;
}

:deep(.vgl-item:not(.vgl-item--placeholder)) {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm;
  transition: all 0.2s ease;
}

:deep(.vgl-item:hover) {
  @apply shadow-md;
}

:deep(.vgl-item--resizing) {
  @apply opacity-80 shadow-lg;
}

:deep(.vgl-item--dragging) {
  @apply opacity-90 shadow-xl;
}

:deep(.vgl-item--placeholder) {
  @apply bg-primary-500/20 border-2 border-dashed border-primary-500 rounded-lg;
}

.device-item {
  @apply w-full h-full flex flex-col p-2;
}

.device-header {
  @apply flex items-center justify-between mb-2;
}

.device-name {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300 truncate;
}

.device-preview {
  @apply flex-1 flex items-center justify-center;
}

.device-screen {
  @apply w-full h-full bg-gray-100 dark:bg-gray-700 rounded border-2 border-gray-300 dark:border-gray-600 flex flex-col items-center justify-center text-center;
}

.device-id {
  @apply text-xs text-gray-500 dark:text-gray-400 mb-1;
}

.device-info {
  @apply text-xs text-gray-600 dark:text-gray-400 space-y-1;
}
</style>
