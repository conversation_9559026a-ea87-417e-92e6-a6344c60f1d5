---
layout: home

hero:
  name: Grid Layout Plus
  text: 一个 Vue 3 的可拖拽、可缩放的布局
  tagline: 一个适用于 Vue 3 栅格布局系统，灵感源自于 React Grid Layout
  image: /grid-layout-plus.svg
  actions:
    - theme: brand
      text: 马上开始
      link: /zh/guide/installation
    - theme: alt
      text: 在 GitHub 查看
      link: https://github.com/qmhc/grid-layout-plus

features:
  - title: ✥ 可拖拽部件
    details:
  - title: ⇲ 可缩放部件
    details:
  - title: 静态部件
    details: 
  - title: 拖拽和调整大小时进行边界检查
    details: 
  - title: 增减部件时避免重建栅格
    details: 
  - title: 可序列化和还原的布局
    details: 
  - title: 自动化 RTL 支持
    details: 
  - title: 响应式
    details: 
  - title: 每个元素具有单独（最大/小）高/宽
    details: 
---
