---
layout: home

hero:
  name: Grid Layout Plus
  text: A draggable and resizable grid layout for Vue 3
  tagline: A grid layout system for Vue 3. Heavily inspired by React Grid Layout.
  image: /grid-layout-plus.svg
  actions:
    - theme: brand
      text: Get Started
      link: /guide/installation
    - theme: alt
      text: View on GitHub
      link: https://github.com/qmhc/grid-layout-plus

features:
  - title: ✥ Draggable widgets
    details:
  - title: ⇲ Resizable widgets
    details:
  - title: Static widgets
    details: 
  - title: Bounds checking for dragging and resizing
    details: 
  - title: Widgets can be added or removed without rebuilding grid
    details: 
  - title: Layout can be serialized and restored
    details: 
  - title: Automatic RTL support
    details: 
  - title: Responsive
    details: 
  - title: Min/max w/h per item
    details: 
---
