{"common.yes": "Yes", "common.no": "No", "common.cancel": "Cancel", "common.confirm": "Confirm", "common.restart": "<PERSON><PERSON>", "common.default": "<PERSON><PERSON><PERSON>", "common.tips": "Tips", "common.open": "Open", "common.input.placeholder": "Please input", "common.success": "Operation successful", "common.success.batch": "Batch operation success", "common.fail": "Operation Failed", "common.save": "Save", "common.saveSuccess": "Save successful", "common.saveError": "Save failed", "common.starting": "Starting", "common.loading": "Loading", "common.search": "Search", "common.batch": "<PERSON><PERSON>", "common.device": "<PERSON><PERSON>", "common.progress": "In Progress", "common.finished": "Finished", "common.stop": "Stop", "common.remove": "Remove", "common.select.please": "Please Select", "common.required": "This field cannot be empty", "common.download": "Download", "common.downloading": "Downloading", "common.delete": "Delete", "common.name": "Name", "common.size": "Size", "common.warning": "Warning", "common.info": "Message", "common.danger": "Error", "common.connecting": "Connecting", "common.language.name": "Language", "common.language.placeholder": "Select language", "time.update": "Update Time", "time.unit.month": "month", "time.unit.week": "week", "time.unit.day": "day", "time.unit.hour": "hour", "time.unit.minute": "minute", "time.unit.second": "second", "time.unit.millisecond": "millisecond", "appClose.name": "Close Main Panel", "appClose.question": "Ask Every Time", "appClose.minimize": "Minimize to Tray", "appClose.quit": "Exit Directly", "appClose.quit.cancel": "Cancel Exit", "appClose.quit.loading": "Stopping the service...", "appClose.message": "Are you sure you want to exit?", "appClose.remember": "Remember the Choice?", "dependencies.lack.title": "Notice", "dependencies.lack.content": "This software relies on {name}. Please ensure that the mentioned dependencies are correctly installed, or manually configure the location of the dependencies in the preferences.", "device.list": "Devices", "device.list.empty": "No devices detected", "device.serial": "Serial", "device.name": "Name", "device.remark": "Remark", "device.permission.error": "Device permission error, please reconnect device and allow USB debugging", "device.terminal.name": "Terminal", "device.status": "Status", "device.status.connected": "Connected", "device.status.offline": "Offline", "device.status.unauthorized": "Unauthorized", "device.status.authorizing": "authorizing", "device.battery": "Device Battery", "device.isCharging": "Charging Status", "device.temperature": "Device Temperature", "device.powerSource": "Power Source", "device.voltage": "Device Voltage", "device.task.name": "Scheduled Task", "device.task.tips": " Note: Please ensure that your computer stays awake, otherwise scheduled tasks will not be executed properly.", "device.task.list": "Task List", "device.task.type": "Task Type", "device.task.frequency": "Execution Frequency", "device.task.frequency.timeout": "Single execution", "device.task.frequency.interval": "Periodic repetition", "device.task.timeout": "Execution Time", "device.task.timeout.tips": "Cannot be earlier than the current time", "device.task.timeout.expired": "The task has expired", "device.task.interval": "Repeat Interval", "device.task.devices": "Involved Devices", "device.task.noRepeat": "No Repeat", "device.task.restart": "Execute Again", "device.task.extra.app": "Select Application", "device.task.extra.shell": "Select Script", "device.wireless.name": "Wireless", "device.wireless.mode": "Wireless", "device.wireless.mode.error": "Do not get the local area network connection address, please check the network", "device.wireless.connect.qr": "QR Code Connection", "device.wireless.connect.qr.pairing": "Pairing", "device.wireless.connect.qr.connecting": "Connecting", "device.wireless.connect.qr.connecting-fallback": "Connecting", "device.wireless.connect.qr.connected": "Connection Successful", "device.wireless.connect.qr.error": "QR Code Connection", "device.wireless.connect.name": "Connect", "device.wireless.connect.error.title": "Connect failed", "device.wireless.connect.error.detail": "Error details", "device.wireless.connect.error.reasons[0]": "Possible reasons:", "device.wireless.connect.error.reasons[1]": "Incorrect IP or port", "device.wireless.connect.error.reasons[2]": "<PERSON><PERSON> not paired", "device.wireless.connect.error.reasons[3]": "IP not in same subnet", "device.wireless.connect.error.reasons[4]": "adb path error", "device.wireless.connect.error.reasons[5]": "Other unknown error", "device.wireless.connect.error.confirm": "Wireless pair", "device.wireless.connect.error.cancel": "@:common.cancel", "device.wireless.connect.error.no-address": "Wireless debug address cannot be empty", "device.wireless.connect.success": "Connect success", "device.wireless.connect.batch.name": "Connect all historical devices", "device.wireless.disconnect.start": "Disconnect", "device.wireless.disconnect.progress": "Disconnecting", "device.wireless.disconnect.success": "Disconnected", "device.wireless.pair": "Wireless Pair", "device.wireless.pair.tips": "Get the following info from Developer options -> Wireless debugging -> Pair device", "device.wireless.pair.address": "Pair IP Address", "device.wireless.pair.address.message": "Pair address cannot be empty", "device.wireless.pair.address.placeholder": "Input pair IP address", "device.wireless.pair.port": "Pair Port", "device.wireless.pair.port.message": "Pair port cannot be empty", "device.wireless.pair.port.placeholder": "Input pair port", "device.wireless.pair.code": "Pair Code", "device.wireless.pair.code.message": "Pair code cannot be empty", "device.wireless.pair.code.placeholder": "Input pair code", "device.reset.title": "Operation Failed", "device.reset.reasons[0]": "Typically, this may be caused by incompatible configurations. Do you want to reset preferences?", "device.reset.reasons[1]": "Note: After resetting, any previously saved configurations will be cleared, so it is recommended to back up your configurations before performing the reset operation.", "device.reset.confirm": "Reset Preferences", "device.reset.cancel": "@:common.cancel", "device.reset.success": "Success, please try again", "device.refresh.name": "Refresh", "device.restart.name": "<PERSON><PERSON>", "device.log.name": "Logs", "device.arrange.name": "Window Arrange", "device.arrange.title": "Window Position & Size Arrangement", "device.arrange.addDevice": "Add <PERSON>", "device.arrange.globalConfig": "Global Config", "device.arrange.screenSize": "Screen Size", "device.arrange.add": "Add", "device.arrange.added": "Added", "device.arrange.defaultWidth": "<PERSON><PERSON><PERSON>", "device.arrange.defaultHeight": "Default Height", "device.arrange.reset": "Reset Layout", "device.arrange.preview": "Preview", "device.arrange.previewTip": "Preview feature is under development", "device.mirror.start": "Connect", "device.record.progress": "Recording", "device.record.success.title": "Record Success", "device.record.success.message": "Open record location?", "device.actions.more.name": "Extra", "device.actions.more.record.name": "Start Recording", "device.actions.more.camera.name": "Startup Camera", "device.actions.more.recordCamera.name": "Record Camera", "device.actions.more.recordAudio.name": "Record Audio", "device.actions.more.otg.name": "Startup OTG", "device.actions.more.custom.name": "Custom Startup", "device.control.name": "Control", "device.control.more": "More Controls", "device.control.install": "Install APP", "device.control.install.placeholder": "Select app to install", "device.control.install.progress": "Installing app to {deviceName}...", "device.control.install.success": "Successfully installed {totalCount} apps to {deviceName}, {successCount} succeeded, {failCount} failed", "device.control.install.success.single": "Successfully installed app to {deviceName}", "device.control.install.error": "Install failed, please check app and try again", "device.control.file.name": "File Manager", "device.control.file.push": "Push File", "device.control.file.push.placeholder": "Please select the file to push", "device.control.file.push.loading": "Push file", "device.control.file.push.success.name": "Push files successfully", "device.control.file.push.success": "Successfully pushed {totalCount} files to {deviceName}, {successCount} succeeded, and {failCount} failed", "device.control.file.push.success.single": "Files successfully pushed to {deviceName}", "device.control.file.push.error": "Failed to push the file, please check the file and try again", "device.control.file.manager.storage": "Internal Storage", "device.control.file.manager.add": "New Folder", "device.control.file.manager.upload": "Upload File", "device.control.file.manager.upload.directory": "Upload Directory", "device.control.file.manager.download": "Download File", "device.control.file.manager.download.tips": "Are you sure you want to download the selected content?", "device.control.file.manager.delete.tips": "Are you sure you want to delete the selected content?", "device.control.terminal.command.name": "Execute Command", "device.control.terminal.script.name": "Execute Script", "device.control.terminal.script.select": "Please select the script you want to execute", "device.control.terminal.script.push.loading": "Push script...", "device.control.terminal.script.push.success": "Push script success", "device.control.terminal.script.enter": "Please enter the Enter key to confirm the execution of the script", "device.control.terminal.script.success": "<PERSON><PERSON><PERSON> execution successfully", "device.control.capture": "Screenshot", "device.control.capture.progress": "Capturing screenshot for {deviceName}...", "device.control.capture.success.message": "Open screenshot location?", "device.control.capture.success.message.title": "Screenshot Success", "device.control.reboot": "Reboot", "device.control.turnScreenOff": "Turn screen off", "device.control.turnScreenOff.tips": "Turn off the screen while maintaining control (Experimental): This action will create an EscrcpyHelper process; manually closing this process will reopen the screen.", "device.control.startApp": "Start APP", "device.control.startApp.useMainScreen": "Open using main screen", "device.control.power": "Power", "device.control.power.tips": "Turn screen on/off", "device.control.notification": "Notification", "device.control.notification.tips": "Open notification panel", "device.control.return": "Back", "device.control.home": "Home", "device.control.switch": "Switch", "device.control.gnirehtet": "Gnirehtet", "device.control.gnirehtet.tips": "Gnirehtet provides reverse tethering for Android; Note: Initial connection requires authorization on the device.", "device.control.gnirehtet.start": "Start Service", "device.control.gnirehtet.start.success": "Gnirehtet reverse tethering feature started successfully", "device.control.gnirehtet.stop": "Stop Service", "device.control.gnirehtet.stop.success": "Service stopped successfully", "device.control.gnirehtet.running": "Service Running", "device.control.gnirehtet.stopping": "Service Stopping", "device.control.mirror-group.name": "Mirror Group", "device.control.mirror-group.tips": "When enabled, can mirror multiple simulated secondary displays and achieve multi-screen collaboration by operating each mirrored window. Note this requires ROM support and desktop mode enabled.", "device.control.mirror-group.open": "Open {num} windows", "device.control.mirror-group.close": "Close auxiliary display devices", "device.control.mirror-group.appClose.tips": "Used to solve the issue of automatic closure failure after certain devices exit all control windows.", "device.control.volume.name": "Volume", "device.control.volume-up.name": "Volume Up", "device.control.volume-down.name": "Volume Down", "device.control.volume-mute.name": "Mute", "device.control.rotation.name": "Rotation", "device.control.rotation.vertically": "Vertically", "device.control.rotation.horizontally": "Horizontally", "device.control.rotation.auto": "Auto", "device.control.rotation.disable": "Disable", "preferences.name": "Preferences", "preferences.reset": "Reset to De<PERSON>ult", "preferences.scope.global": "Global", "preferences.scope.placeholder": "Preference scope", "preferences.scope.no-data": "No data", "preferences.scope.details[0]": "Set global or per-device preferences", "preferences.scope.details[1]": "Global: Apply to all devices", "preferences.scope.details[2]": "Per-device: Override global settings for one device", "preferences.config.import.name": "Import", "preferences.config.import.placeholder": "Select config file", "preferences.config.import.success": "Import success", "preferences.config.export.name": "Export", "preferences.config.export.message": "Export config", "preferences.config.export.placeholder": "Select export location", "preferences.config.export.success": "Export success", "preferences.config.edit.name": "Edit", "preferences.config.reset.name": "Reset", "preferences.common.name": "Common", "preferences.common.theme.name": "Theme", "preferences.common.theme.placeholder": "Set theme", "preferences.common.theme.options[0]": "Light Mode", "preferences.common.theme.options[1]": "Dark Mode", "preferences.common.theme.options[2]": "Following system", "preferences.common.debug.name": "Debug", "preferences.common.debug.placeholder": "Enable debug mode", "preferences.common.debug.tips": "Show debug info in log, disable to improve performance. Restart to take effect.", "preferences.common.file.name": "File Location", "preferences.common.file.placeholder": "User Desktop", "preferences.common.file.tips": "Location to save screenshots and recordings", "preferences.common.adb.name": "Adb Path", "preferences.common.adb.placeholder": "Custom adb path", "preferences.common.adb.tips": "adb path to connect device", "preferences.common.scrcpy.name": "Scrcpy Path", "preferences.common.scrcpy.placeholder": "Custom scrcpy path", "preferences.common.scrcpy.tips": "scrcpy path to control device", "preferences.common.scrcpy.append.name": "Scrcpy Arguments", "preferences.common.scrcpy.append.placeholder": "Append additional arguments to the scrcpy command", "preferences.common.scrcpy.append.tips": "Note: The entered arguments will be directly appended to the scrcpy command without filtering duplicate arguments.", "preferences.common.gnirehtet.name": "Gnirehtet Path", "preferences.common.gnirehtet.placeholder": "Custom gnirehtet path", "preferences.common.gnirehtet.tips": "The path for gnirehtet used to provide reverse tethering for devices.", "preferences.common.gnirehtet.fix.name": "Gnirehtet Fix", "preferences.common.gnirehtet.fix.placeholder": "When enabled, it will disable the installation check of gnirehtet.apk, which may improve connection issues on some devices.", "preferences.common.gnirehtet.fix.tips": "Note: This may cause gnirehtet.apk to be reinstalled every time it starts.", "preferences.common.gnirehtet.append.name": "Gnirehtet Arguments", "preferences.common.gnirehtet.append.placeholder": "Append additional arguments to the gnirehtet command", "preferences.common.gnirehtet.append.tips": "Note: The entered arguments will be directly appended to the gnirehtet command without filtering duplicate arguments.", "preferences.common.floatControl.name": "Floating Control Bar", "preferences.common.floatControl.placeholder": "Once enabled, the device floating control bar will automatically open during mirroring", "preferences.common.auto-connect.name": "Auto Connect", "preferences.common.auto-connect.placeholder": "When enabled, the software will attempt to automatically connect to historical devices upon startup.", "preferences.common.auto-mirror.name": "Auto Mirror", "preferences.common.auto-mirror.placeholder": "When enabled, devices in the device list will automatically execution the mirror.", "preferences.common.edgeHidden.name": "Auto-hide Main Panel", "preferences.common.edgeHidden.placeholder": "When enabled, the main panel will automatically hide when mouse moves near screen edge", "preferences.common.edgeHidden.tips": "Note: Changes require application restart to take effect", "preferences.common.imeFix.name": "Start APP Keyboard Fix", "preferences.common.imeFix.placeholder": "When enabled, this will resolve the issue where the input method cannot be displayed in the current mirror window when the APP starts.", "preferences.common.imeFix.tips": "Note: This feature is only supported in scrcpy v3.2 and above. Using it with lower versions will cause errors.", "preferences.video.name": "Video", "preferences.video.disable-video.name": "Disable Video Forwarding", "preferences.video.disable-video.placeholder": "Video forwarding will be disabled when enabled", "preferences.video.video-source.name": "Video Source", "preferences.video.video-source.placeholder": "Device display", "preferences.video.video-source.display": "Display", "preferences.video.video-source.camera": "Camera", "preferences.video.resolution.name": "<PERSON>", "preferences.video.resolution.placeholder": "<PERSON><PERSON>, format: 1080", "preferences.video.bit.name": "Video Bit Rate", "preferences.video.bit.placeholder": "8000000, format: 8M，8000000", "preferences.video.video-code.name": "Video Codec", "preferences.video.video-code.placeholder": "h.264", "preferences.video.refresh-rate.name": "Frame Rate", "preferences.video.refresh-rate.placeholder": "60", "preferences.video.display-orientation.name": "Display Orientation", "preferences.video.display-orientation.placeholder": "Device orientation", "preferences.video.angle.name": "Rotation Angle", "preferences.video.angle.placeholder": "No rotation, format: 15", "preferences.video.angle.tips": "Note: This option is also effective during recording", "preferences.video.screen-cropping.name": "Crop", "preferences.video.screen-cropping.placeholder": "No crop, format: 1224:1440:0:0", "preferences.video.display.name": "Display", "preferences.video.display.placeholder": "Main display", "preferences.video.video-buffer.name": "Video Buffer", "preferences.video.video-buffer.placeholder": "0", "preferences.video.receiver-buffer.name": "Receiver B<PERSON>er (v412)", "preferences.video.receiver-buffer.placeholder": "0", "preferences.device.name": "<PERSON><PERSON>", "preferences.device.show-touch.name": "Show Touches", "preferences.device.show-touch.placeholder": "Enable touch feedback dots", "preferences.device.show-touch.tips": "Physical device only", "preferences.device.stay-awake.name": "Stay Awake", "preferences.device.stay-awake.placeholder": "Prevent device sleep", "preferences.device.stay-awake.tips": "Wired only", "preferences.device.turnScreenOff.name": "Turn Off Screen", "preferences.device.turnScreenOff.placeholder": "Turn off device screen when controlling", "preferences.device.screenOffTimeout.name": "Screen Timeout", "preferences.device.screenOffTimeout.placeholder": "<PERSON><PERSON>", "preferences.device.screenOffTimeout.tips": "Modify the screen timeout setting and restore the device default on exit", "preferences.device.control-end-video.name": "Turn Off at End Screen", "preferences.device.control-end-video.placeholder": "Turn off screen when control ends", "preferences.device.control-in-stop-charging.name": "Stop Charging", "preferences.device.control-in-stop-charging.placeholder": "Stop charging when controlling", "preferences.device.control-in-stop-charging.tips": "Stop charging when controlling", "preferences.device.display-overlay.name": "Simulated Display", "preferences.device.display-overlay.placeholder": "<PERSON><PERSON>, Format: 1920x1080/420, 1920x1080, /240", "preferences.device.display-overlay.tips": "Used to adjust the size and resolution of simulated auxiliary displays, launching applications and multi-screen collaboration (mirroring group) depend on this option", "preferences.window.name": "Window", "preferences.window.borderless.name": "Borderless", "preferences.window.borderless.placeholder": "Borderless control window", "preferences.window.full-screen.name": "Fullscreen", "preferences.window.full-screen.placeholder": "Fullscreen control window", "preferences.window.always-top.name": "Always on Top", "preferences.window.always-top.placeholder": "Keep control window topmost", "preferences.window.disable-screen-saver.name": "Disable Screensaver", "preferences.window.disable-screen-saver.placeholder": "Disable computer screensaver", "preferences.window.size.width": "Window Width", "preferences.window.size.width.placeholder": "<PERSON><PERSON>", "preferences.window.size.width.tips": "Note: Changing this setting may result in blurry display.", "preferences.window.size.height": "Window Height", "preferences.window.size.height.placeholder": "Device Height", "preferences.window.size.height.tips": "Note: Changing this setting may result in blurry display.", "preferences.window.position.x": "Window X Position", "preferences.window.position.x.placeholder": "Relative to Desktop Center", "preferences.window.position.y": "Window Y Position", "preferences.window.position.y.placeholder": "Relative to Desktop Center", "preferences.record.name": "Recording", "preferences.record.format.name": "Format", "preferences.record.format.placeholder": "mp4", "preferences.record.format.audio.name": "Audio Format", "preferences.record.format.audio.placeholder": "opus", "preferences.record.time-limit.name": "Recording Time Limit", "preferences.record.time-limit.placeholder": "No time limit", "preferences.record.orientation.name": "Video Direction", "preferences.record.orientation.placeholder": "Device Orientation", "preferences.record.no-video-playback.name": "Disable Video Playback", "preferences.record.no-video-playback.placeholder": "Video playback will be disabled during recording when enabled", "preferences.record.no-video-playback.tips": "Note: Video will still be recorded, just playback disabled", "preferences.record.no-audio-playback.name": "Disable Audio Playback", "preferences.record.no-audio-playback.placeholder": "Audio playback will be disabled during recording when enabled", "preferences.record.no-audio-playback.tips": "Note: Audio will still be recorded, just playback disabled", "preferences.audio.name": "Audio", "preferences.audio.disable-audio.name": "Disable Audio Forwarding", "preferences.audio.disable-audio.placeholder": "Audio forwarding will be disabled when enabled", "preferences.audio.disable-audio.tips": "If your device audio capture is abnormal, you can open this option to ensure that you can open the mirror normally", "preferences.audio.audioDup.name": "Keep device audio", "preferences.audio.audioDup.placeholder": "When enabled, audio will continue to play on the device during mirroring", "preferences.audio.audioDup.tips": "Note: This option requires Android 13+ and apps can opt out (in which case they won't be captured)", "preferences.audio.audio-source.name": "Audio Source", "preferences.audio.audio-source.placeholder": "Device Audio Output", "preferences.audio.audio-source.tips": "Tip: Selecting 'Microphone' as the source will allow you to record audio.", "preferences.audio.audio-source.output": "Forwards the whole audio output, and disables playback on the device", "preferences.audio.audio-source.mic": "Captures the microphone", "preferences.audio.audio-source.playback": "Captures the audio playback (Android apps can opt out, so the whole output is not necessarily captured)", "preferences.audio.audio-source.mic-unprocessed": "Captures the microphone unprocessed (raw) sound", "preferences.audio.audio-source.mic-camcorder": "Captures the microphone tuned for video recording, with the same orientation as the camera if available", "preferences.audio.audio-source.mic-voice-recognition": "Captures the microphone tuned for voice recognition", "preferences.audio.audio-source.mic-voice-communication": "Captures the microphone tuned for voice communications (it will for instance take advantage of echo cancellation or automatic gain control if available) ", "preferences.audio.audio-source.voice-call": "Captures voice call", "preferences.audio.audio-source.voice-call-uplink": "Captures voice call uplink only", "preferences.audio.audio-source.voice-call-downlink": "Captures voice call downlink only", "preferences.audio.audio-source.voice-performance": "Captures audio meant to be processed for live performance (karaoke), includes both the microphone and the device playback", "preferences.audio.audio-code.name": "Audio Codec", "preferences.audio.audio-code.placeholder": "opus", "preferences.audio.audio-bit-rate.name": "Audio Bit Rate", "preferences.audio.audio-bit-rate.placeholder": "128000, format: 128K，128000", "preferences.audio.audio-bit-rate.tips": "Note: This option does not apply to RAW audio codecs.", "preferences.audio.audio-buffer.name": "Audio Buffer", "preferences.audio.audio-buffer.placeholder": "0", "preferences.audio.audio-output-buffer.name": "Audio Output Buffer", "preferences.audio.audio-output-buffer.placeholder": "5", "preferences.input.name": "Input", "preferences.input.mouse.name": "Mouse Mode", "preferences.input.mouse.placeholder": "sdk", "preferences.input.mouse.tips": "Set mouse input mode", "preferences.input.mouse.options[0].placeholder": "<PERSON><PERSON><PERSON>", "preferences.input.mouse.options[1].placeholder": "Simulates a physical HID mouse using the UHID kernel module on the device", "preferences.input.mouse.options[2].placeholder": "Simulates a physical HID mouse using the AOAv2 protocol", "preferences.input.mouse.options[3].placeholder": "Disable mouse input", "preferences.input.mouse-bind.name": "<PERSON> Bind", "preferences.input.mouse-bind.tips": "This option allows customization of mouse button functions. It uses two sets of 4-character sequences to define primary and secondary (Shift key) bindings. Each character represents a mouse button (right, middle, 4th, 5th) and can be set to: '+' forward to device, '-' ignore, 'b' back, 'h' home, 's' app switch, 'n' expand notification panel. For example, --mouse-bind=bhsn:++++ means primary bindings are back/home/<USER>/notification, while secondary bindings all forward to the device.", "preferences.input.mouse-bind.placeholder": "bhsn:++++", "preferences.input.keyboard.name": "Keyboard Mode", "preferences.input.keyboard.placeholder": "sdk", "preferences.input.keyboard.tips": "Set keyboard input mode", "preferences.input.keyboard.options[0].placeholder": "<PERSON><PERSON><PERSON>", "preferences.input.keyboard.options[1].placeholder": "Simulates a physical HID keyboard using the UHID kernel module on the device", "preferences.input.keyboard.options[2].placeholder": "Simulates a physical HID keyboard using the AOAv2 protocol", "preferences.input.keyboard.options[3].placeholder": "Disable keyboard input", "preferences.input.keyboard.inject.name": "Keyboard Inject", "preferences.input.keyboard.inject.placeholder": "default", "preferences.input.keyboard.inject.tips": "Set keyboard text injection first option", "preferences.input.keyboard.inject.options[0].placeholder": "Inject letters as text", "preferences.input.keyboard.inject.options[1].placeholder": "Force always inject the original button event", "preferences.input.gamepad.name": "Gamepad", "preferences.input.gamepad.placeholder": "Disabled", "preferences.input.gamepad.tips": "This option allows connecting a gamepad (PS4/PS5 or Xbox) to your computer to play Android games. Note: The game being played must support gamepad input.", "preferences.camera.name": "Camera", "preferences.camera.camera-facing.name": "Camera Source", "preferences.camera.camera-facing.placeholder": "Device camera source", "preferences.camera.camera-facing.front": "Front Camera", "preferences.camera.camera-facing.back": "Back Camera", "preferences.camera.camera-facing.external": "External Camera", "preferences.camera.camera-size.name": "Camera Size", "preferences.camera.camera-size.placeholder": "Device camera size, format: 1920x1080", "preferences.camera.camera-ar.name": "Camera Aspect Ratio", "preferences.camera.camera-ar.placeholder": "device camera aspect ratio, format: 4:3, sensor, 1.6 etc", "preferences.camera.camera-fps.name": "Camera Frame Rate", "preferences.camera.camera-fps.placeholder": "Device camera frame rate", "about.name": "About", "about.description": "📱 Display and control your Android device graphically with scrcpy, devices powered by Electron.", "about.update": "Check for Updates", "about.update-not-available": "Already latest version", "about.update-error.title": "Update check failed", "about.update-error.message": "You may need a proxy. Download manually from releases page?", "about.update-downloaded.title": "New version downloaded", "about.update-downloaded.message": "Restart to update now?", "about.update-downloaded.confirm": "Update", "about.update-available.title": "Update Available", "about.update-available.confirm": "Update", "about.update.progress": "Updating...", "about.donate.title": "Donate", "about.donate.description": "If this project has helped you, you can buy me a coffee to keep me energized to improve it 😛", "about.docs.name": "Help Docs", "desktop.shortcut.add": "Add to Desktop Shortcut"}